.control-center {
  padding: 1em;
  border-radius: 12px;
  background-color: #1e1e2e;
  color: #cdd6f4;
  min-width: 300px;
  font-size: 14px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);
  .title {
    font-weight: bold;
    font-size: 1.2em;
    margin-bottom: 0.5em;
    text-align: center;
  }
  .item {
    margin-bottom: 1em;
    display: flex;
    flex-direction: column;
    gap: 0.3em;
  }
  .controls, .session {
    flex-direction: row;
    justify-content: space-between;
  }
  slider {
    accent-color: #89b4fa;
  }
  button {
    background-color: #313244;
    border: none;
    border-radius: 8px;
    padding: 0.5em 1em;
    color: #cdd6f4;
    font-weight: bold;
    transition: background 0.2s;
  }
  button:hover {
    background-color: #45475a;
  }
}

#!/bin/bash

# Script de demostración del calendario EWW
echo "🎯 Demostración del Widget de Calendario EWW"
echo "============================================="

# Función para mostrar el estado actual
show_status() {
    echo -e "\n📊 Estado actual del calendario:"
    CALENDAR_DATA=$(~/.config/eww/scripts/calendar.sh)
    echo "$CALENDAR_DATA" | jq '.'
}

# Función para pausa con mensaje
pause_with_message() {
    echo -e "\n⏸️  $1"
    echo "Presiona Enter para continuar..."
    read
}

echo -e "\n🚀 Iniciando demostración..."

# 1. Mostrar estado inicial
echo -e "\n1️⃣ Estado inicial del calendario"
show_status

pause_with_message "Ahora abriremos el calendario. Observa la ventana que aparece."

# 2. Abrir calendario
echo -e "\n📅 Abriendo calendario..."
eww open calendar
sleep 2

pause_with_message "El calendario está abierto. Ahora navegaremos al mes siguiente."

# 3. Navegar al mes siguiente
echo -e "\n➡️ Navegando al mes siguiente..."
~/.config/eww/scripts/calendar.sh next
show_status
sleep 2

pause_with_message "Ahora navegaremos al mes anterior (2 veces para volver al mes original)."

# 4. Navegar al mes anterior
echo -e "\n⬅️ Navegando al mes anterior..."
~/.config/eww/scripts/calendar.sh prev
show_status
sleep 2

pause_with_message "Navegando una vez más al mes anterior..."

~/.config/eww/scripts/calendar.sh prev
show_status
sleep 2

pause_with_message "Ahora volveremos al mes actual."

# 5. Volver al mes actual
echo -e "\n🏠 Volviendo al mes actual..."
~/.config/eww/scripts/calendar.sh reset
show_status
sleep 2

pause_with_message "Ahora mostraremos los días generados para el calendario."

# 6. Mostrar días generados
echo -e "\n📋 Días generados para el calendario:"
DAYS_DATA=$(~/.config/eww/scripts/get-calendar-days.sh)
echo "Total de días: $(echo "$DAYS_DATA" | jq 'length')"
echo -e "\nPrimeros 7 días (primera semana):"
echo "$DAYS_DATA" | jq '.[0:7]'

echo -e "\nÚltimos 7 días (última semana):"
echo "$DAYS_DATA" | jq '.[-7:]'

pause_with_message "Demostración de funcionalidades completada. Cerraremos el calendario."

# 7. Cerrar calendario
echo -e "\n❌ Cerrando calendario..."
eww close calendar

echo -e "\n✅ Demostración completada!"
echo -e "\n📝 Resumen de funcionalidades demostradas:"
echo "   • Apertura y cierre del calendario"
echo "   • Navegación entre meses (siguiente/anterior)"
echo "   • Reset al mes actual"
echo "   • Generación de días del calendario"
echo "   • Visualización de datos JSON"

echo -e "\n🎯 Para usar el calendario:"
echo "   • Abrir: eww open calendar"
echo "   • Cerrar: eww close calendar"
echo "   • Probar: bash ~/.config/eww/scripts/test-calendar.sh"

echo -e "\n🎨 El calendario incluye:"
echo "   • Navegación con botones ‹ y ›"
echo "   • Día actual resaltado"
echo "   • Días de otros meses en gris"
echo "   • Diseño responsivo y moderno"

echo -e "\n¡Gracias por probar el widget de calendario EWW! 📅"

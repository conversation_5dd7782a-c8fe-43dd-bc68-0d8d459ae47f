#!/bin/bash

# Obtener el mes y año actual
CURRENT_MONTH=$(date +%m)
CURRENT_YEAR=$(date +%Y)
CURRENT_DAY=$(date +%d)

# Inicializar mes y año
if [ "$1" = "next" ]; then
    MONTH=$((10#$CURRENT_MONTH + 1))
    YEAR=$CURRENT_YEAR
    if [ $MONTH -gt 12 ]; then
        MONTH=1
        YEAR=$((YEAR + 1))
    fi
elif [ "$1" = "prev" ]; then
    MONTH=$((10#$CURRENT_MONTH - 1))
    YEAR=$CURRENT_YEAR
    if [ $MONTH -lt 1 ]; then
        MONTH=12
        YEAR=$((YEAR - 1))
    fi
else
    MONTH=$((10#$CURRENT_MONTH))
    YEAR=$CURRENT_YEAR
fi

# Formatear el mes a dos dígitos
MONTH=$(printf "%02d" $MONTH)

# Obtener el primer día del mes y el número de días
FIRST_DAY=$(date -d "$YEAR-$MONTH-01" +%u) # 1-7 (Lun-Dom)
DAYS_IN_MONTH=$(date -d "$YEAR-$MONTH-01 + 1 month - 1 day" +%d)

# Nombres de los meses
MONTH_NAMES=("Enero" "Febrero" "Marzo" "Abril" "Mayo" "Junio" "Julio" "Agosto" "Septiembre" "Octubre" "Noviembre" "Diciembre")
MONTH_NAME=${MONTH_NAMES[$((10#$MONTH - 1))]}

echo "{\"month\":\"$MONTH_NAME $YEAR\",\"days\":\"$DAYS_IN_MONTH\",\"start_day\":$FIRST_DAY,\"current_day\":$CURRENT_DAY,\"current_month\":$CURRENT_MONTH,\"current_year\":$CURRENT_YEAR,\"display_month\":$MONTH,\"display_year\":$YEAR}"

#!/bin/bash

# Archivo de estado para mantener el mes/año actual
STATE_FILE="/tmp/eww_calendar_state"

# Obtener el mes y año actual del sistema
CURRENT_MONTH=$(date +%m)
CURRENT_YEAR=$(date +%Y)
CURRENT_DAY=$(date +%d)

# Leer el estado anterior si existe
if [ -f "$STATE_FILE" ]; then
    DISPLAY_MONTH=$(cat "$STATE_FILE" | cut -d',' -f1)
    DISPLAY_YEAR=$(cat "$STATE_FILE" | cut -d',' -f2)
else
    DISPLAY_MONTH=$CURRENT_MONTH
    DISPLAY_YEAR=$CURRENT_YEAR
fi

# Procesar comandos de navegación
if [ "$1" = "next" ]; then
    DISPLAY_MONTH=$((10#$DISPLAY_MONTH + 1))
    if [ $DISPLAY_MONTH -gt 12 ]; then
        DISPLAY_MONTH=1
        DISPLAY_YEAR=$((DISPLAY_YEAR + 1))
    fi
elif [ "$1" = "prev" ]; then
    DISPLAY_MONTH=$((10#$DISPLAY_MONTH - 1))
    if [ $DISPLAY_MONTH -lt 1 ]; then
        DISPLAY_MONTH=12
        DISPLAY_YEAR=$((DISPLAY_YEAR - 1))
    fi
elif [ "$1" = "reset" ]; then
    DISPLAY_MONTH=$CURRENT_MONTH
    DISPLAY_YEAR=$CURRENT_YEAR
fi

# Formatear el mes a dos dígitos
DISPLAY_MONTH=$(printf "%02d" $DISPLAY_MONTH)

# Guardar el estado actual
echo "$DISPLAY_MONTH,$DISPLAY_YEAR" > "$STATE_FILE"

# Obtener el primer día del mes y el número de días
FIRST_DAY=$(date -d "$DISPLAY_YEAR-$DISPLAY_MONTH-01" +%u) # 1-7 (Lun-Dom)
DAYS_IN_MONTH=$(date -d "$DISPLAY_YEAR-$DISPLAY_MONTH-01 + 1 month - 1 day" +%d)

# Nombres de los meses
MONTH_NAMES=("Enero" "Febrero" "Marzo" "Abril" "Mayo" "Junio" "Julio" "Agosto" "Septiembre" "Octubre" "Noviembre" "Diciembre")
MONTH_NAME=${MONTH_NAMES[$((10#$DISPLAY_MONTH - 1))]}

echo "{\"month\":\"$MONTH_NAME $DISPLAY_YEAR\",\"days\":\"$DAYS_IN_MONTH\",\"start_day\":$FIRST_DAY,\"current_day\":$CURRENT_DAY,\"current_month\":$CURRENT_MONTH,\"current_year\":$CURRENT_YEAR,\"display_month\":$DISPLAY_MONTH,\"display_year\":$DISPLAY_YEAR}"

#!/bin/bash

# Script de prueba para el calendario EWW
echo "=== Probando el calendario EWW ==="

# Verificar dependencias
echo "1. Verificando dependencias..."
if ! command -v jq &> /dev/null; then
    echo "❌ Error: jq no está instalado. Instálalo con: sudo apt install jq"
    exit 1
fi

if ! command -v date &> /dev/null; then
    echo "❌ Error: date no está disponible"
    exit 1
fi

echo "✅ Dependencias verificadas"

# Probar el script principal
echo -e "\n2. Probando calendar.sh:"
CALENDAR_OUTPUT=$(~/.config/eww/scripts/calendar.sh)
echo "$CALENDAR_OUTPUT" | jq '.'
if [ $? -eq 0 ]; then
    echo "✅ calendar.sh funciona correctamente"
else
    echo "❌ Error en calendar.sh"
    exit 1
fi

# Probar get-calendar-days.sh
echo -e "\n3. Probando get-calendar-days.sh:"
DAYS_OUTPUT=$(~/.config/eww/scripts/get-calendar-days.sh)
echo "$DAYS_OUTPUT" | jq '.[0:7]'  # Mostrar solo los primeros 7 días
if [ $? -eq 0 ]; then
    echo "✅ get-calendar-days.sh funciona correctamente"
    DAYS_COUNT=$(echo "$DAYS_OUTPUT" | jq 'length')
    echo "📊 Total de días generados: $DAYS_COUNT"
else
    echo "❌ Error en get-calendar-days.sh"
    exit 1
fi

# Probar navegación
echo -e "\n4. Probando navegación..."
echo "Mes siguiente:"
~/.config/eww/scripts/calendar.sh next | jq '.month'

echo "Mes anterior (x2):"
~/.config/eww/scripts/calendar.sh prev | jq '.month'

echo "Reset al mes actual:"
~/.config/eww/scripts/calendar.sh reset | jq '.month'

echo "✅ Navegación funciona correctamente"

# Verificar permisos de ejecución
echo -e "\n5. Verificando permisos..."
for script in calendar.sh get-calendar-days.sh; do
    if [ -x ~/.config/eww/scripts/$script ]; then
        echo "✅ $script tiene permisos de ejecución"
    else
        echo "⚠️  $script no tiene permisos de ejecución, corrigiendo..."
        chmod +x ~/.config/eww/scripts/$script
    fi
done

echo -e "\n🎉 Todas las pruebas completadas exitosamente!"
echo "Para abrir el calendario en EWW, ejecuta: eww open calendar"

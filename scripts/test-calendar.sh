#!/bin/bash

# Script para probar la generación del calendario

echo "=== Prueba del widget de calendario ==="
echo ""

# Probar el script del calendario
echo "1. Probando calendar.sh..."
CALENDAR_JSON=$(~/.config/eww/scripts/calendar.sh)
echo "Resultado: $CALENDAR_JSON"

# Probar la generación de días
echo -e "\n2. Probando get-calendar-days.sh..."
DAYS_JSON=$(~/.config/eww/scripts/get-calendar-days.sh)
echo "Días generados: ${#DAYS_JSON} caracteres"

# Mostrar los primeros 200 caracteres del resultado
echo -e "\n3. Vista previa de los datos de días (primeros 200 caracteres):"
echo "${DAYS_JSON:0:200}..."

echo -e "\n4. Verificando dependencias..."
command -v jq >/dev/null 2>&1 || { echo "  ❌ jq no está instalado. Por favor instálalo con 'sudo apt install jq'"; exit 1; }
echo "  ✅ jq está instalado"

# Verificar si hay errores en la sintaxis de los scripts
echo -e "\n5. Verificando sintaxis de los scripts..."
for script in ~/.config/eww/scripts/*.sh; do
  if bash -n "$script"; then
    echo "  ✅ $script: Sintaxis correcta"
  else
    echo "  ❌ $script: Error en la sintaxis"
  fi
done

echo -e "\n=== Prueba completada ==="
echo "Para ver el calendario, ejecuta: eww open calendar"

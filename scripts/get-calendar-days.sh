#!/bin/bash

# Obtener los datos del calendario actual
CALENDAR_JSON=$(~/.config/eww/scripts/calendar.sh)

# Verificar que jq esté disponible
if ! command -v jq &> /dev/null; then
    echo "[]"
    exit 1
fi

# Extraer valores usando jq
MONTH=$(echo "$CALENDAR_JSON" | jq -r '.display_month')
YEAR=$(echo "$CALENDAR_JSON" | jq -r '.display_year')
DAYS_IN_MONTH=$(echo "$CALENDAR_JSON" | jq -r '.days')
START_DAY=$(echo "$CALENDAR_JSON" | jq -r '.start_day')
CURRENT_DAY=$(echo "$CALENDAR_JSON" | jq -r '.current_day')
CURRENT_MONTH=$(echo "$CALENDAR_JSON" | jq -r '.current_month')
CURRENT_YEAR=$(echo "$CALENDAR_JSON" | jq -r '.current_year')

# Verificar que los valores sean válidos
if [ "$MONTH" = "null" ] || [ "$YEAR" = "null" ] || [ "$DAYS_IN_MONTH" = "null" ]; then
    echo "[]"
    exit 1
fi

# Calcular días del mes anterior a mostrar
if [ $START_DAY -eq 1 ]; then
    PREV_MONTH_DAYS_TO_SHOW=0
else
    PREV_MONTH_DAYS_TO_SHOW=$((START_DAY - 1))
fi

# Obtener el último día del mes anterior
if [ "$MONTH" = "01" ]; then
    PREV_MONTH=12
    PREV_YEAR=$((YEAR - 1))
else
    PREV_MONTH=$((10#$MONTH - 1))
    PREV_YEAR=$YEAR
fi
PREV_MONTH=$(printf "%02d" $PREV_MONTH)
PREV_MONTH_DAYS=$(date -d "$PREV_YEAR-$PREV_MONTH-01 + 1 month - 1 day" +%d)

# Inicializar array de días
DAYS_JSON="["
FIRST_ITEM=true

# Agregar días del mes anterior
if [ $PREV_MONTH_DAYS_TO_SHOW -gt 0 ]; then
    for ((i=$((PREV_MONTH_DAYS - PREV_MONTH_DAYS_TO_SHOW + 1)); i<=PREV_MONTH_DAYS; i++)); do
        if [ "$FIRST_ITEM" = false ]; then
            DAYS_JSON+=","
        fi
        DAYS_JSON+="{\"day\":\"$i\",\"is_current_month\":\"false\",\"is_today\":\"false\"}"
        FIRST_ITEM=false
    done
fi

# Agregar días del mes actual
for ((i=1; i<=DAYS_IN_MONTH; i++)); do
    if [ "$FIRST_ITEM" = false ]; then
        DAYS_JSON+=","
    fi
    IS_TODAY="false"
    if [ "$i" = "$CURRENT_DAY" ] && [ "$MONTH" = "$CURRENT_MONTH" ] && [ "$YEAR" = "$CURRENT_YEAR" ]; then
        IS_TODAY="true"
    fi
    DAYS_JSON+="{\"day\":\"$i\",\"is_current_month\":\"true\",\"is_today\":\"$IS_TODAY\"}"
    FIRST_ITEM=false
done

# Calcular cuántos días del siguiente mes necesitamos para completar 6 semanas (42 días)
TOTAL_DAYS_SHOWN=$((PREV_MONTH_DAYS_TO_SHOW + DAYS_IN_MONTH))
NEXT_MONTH_DAYS_TO_SHOW=$((42 - TOTAL_DAYS_SHOWN))

# Agregar días del siguiente mes
if [ $NEXT_MONTH_DAYS_TO_SHOW -gt 0 ]; then
    for ((i=1; i<=NEXT_MONTH_DAYS_TO_SHOW; i++)); do
        if [ "$FIRST_ITEM" = false ]; then
            DAYS_JSON+=","
        fi
        DAYS_JSON+="{\"day\":\"$i\",\"is_current_month\":\"false\",\"is_today\":\"false\"}"
        FIRST_ITEM=false
    done
fi

DAYS_JSON+="]"
echo "$DAYS_JSON"

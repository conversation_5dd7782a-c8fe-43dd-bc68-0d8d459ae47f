#!/bin/bash

# Obtener los datos del calendario actual
CALENDAR_JSON=$(~/.config/eww/scripts/calendar.sh)

# Extraer valores
MONTH=$(echo $CALENDAR_JSON | jq -r '.display_month')
YEAR=$(echo $CALENDAR_JSON | jq -r '.display_year')
DAYS_IN_MONTH=$(echo $CALENDAR_JSON | jq -r '.days')
START_DAY=$(echo $CALENDAR_JSON | jq -r '.start_day')
CURRENT_DAY=$(echo $CALENDAR_JSON | jq -r '.current_day')
CURRENT_MONTH=$(echo $CALENDAR_JSON | jq -r '.current_month')
CURRENT_YEAR=$(echo $CALENDAR_JSON | jq -r '.current_year')

# Calcular días del mes anterior a mostrar
PREV_MONTH_DAYS=$(date -d "$YEAR-$MONTH-01 yesterday" +%d)
PREV_MONTH_DAYS_TO_SHOW=$((START_DAY - 1))

# Inicializar array de días
DAYS_JSON="["

# Agregar días del mes anterior
for ((i=$PREV_MONTH_DAYS - $PREV_MONTH_DAYS_TO_SHOW + 1; i<=$PREV_MONTH_DAYS; i++)); do
    if [ ! -z "$DAYS_JSON" ] && [ "$DAYS_JSON" != "[" ]; then
        DAYS_JSON+=","
    fi
    DAYS_JSON+="{\"day\":\"$i\",\"is_current_month\":false,\"is_today\":false}"
done

# Agregar días del mes actual
for ((i=1; i<=$DAYS_IN_MONTH; i++)); do
    if [ ! -z "$DAYS_JSON" ] && [ "$DAYS_JSON" != "[" ]; then
        DAYS_JSON+=","
    fi
    IS_TODAY=false
    if [ "$i" = "$CURRENT_DAY" ] && [ "$MONTH" = "$CURRENT_MONTH" ] && [ "$YEAR" = "$CURRENT_YEAR" ]; then
        IS_TODAY=true
    fi
    DAYS_JSON+="{\"day\":\"$i\",\"is_current_month\":true,\"is_today\":$IS_TODAY}"
done

# Calcular días del siguiente mes a mostrar
NEXT_MONTH_DAYS_TO_SHOW=$((42 - (PREV_MONTH_DAYS_TO_SHOW + DAYS_IN_MONTH)))
if [ $NEXT_MONTH_DAYS_TO_SHOW -gt 0 ]; then
    for ((i=1; i<=$NEXT_MONTH_DAYS_TO_SHOW; i++)); do
        if [ ! -z "$DAYS_JSON" ] && [ "$DAYS_JSON" != "[" ]; then
            DAYS_JSON+=","
        fi
        DAYS_JSON+="{\"day\":\"$i\",\"is_current_month\":false,\"is_today\":false}"
    done
fi

DAYS_JSON+="]"
echo $DAYS_JSON

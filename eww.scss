// Variables
$bg: #2a2a2a;
$fg: #f8f8f2;
$accent: #bd93f9;
$gray: #44475a;
$light-gray: #6272a4;
$red: #ff5555;

// Estilos generales
* {
  all: unset;
  font-family: "Fira Code", "Font Awesome 6 Free";
  font-size: 12px;
}

// Contenedor principal del calendario
.calendar-container {
  background-color: $bg;
  border-radius: 12px;
  padding: 16px;
  color: $fg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid $gray;
  min-width: 280px;
}

// Encabezado del calendario
.calendar-header {
  margin-bottom: 12px;
}

// Botón de cierre
.close-button {
  color: $light-gray;
  font-size: 18px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    color: $red;
  }
}

// Navegación de mes
.month-navigation {
  .month-year {
    font-size: 16px;
    font-weight: bold;
    margin: 0 16px;
  }

  .nav-button {
    background-color: $gray;
    color: $fg;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 16px;
    font-weight: bold;

    &:hover {
      background-color: $light-gray;
    }
  }
}

// Días de la semana
.weekdays {
  margin: 12px 0 8px 0;

  .weekday {
    font-weight: bold;
    color: $accent;
    padding: 8px 4px;
    font-size: 11px;
  }
}

// Cuadrícula del calendario
.calendar-grid {
  .day {
    min-width: 36px;
    min-height: 36px;
    border-radius: 6px;
    border: 1px solid transparent;

    .day-label {
      font-size: 13px;
      font-weight: 500;
    }

    &.current-month {
      color: $fg;

      &:hover {
        background-color: $gray;
      }

      &.today {
        background-color: $accent;
        color: $bg;
        font-weight: bold;

        .day-label {
          font-weight: bold;
        }
      }
    }

    &.other-month {
      color: rgba($fg, 0.3);

      &:hover {
        color: rgba($fg, 0.5);
        background-color: rgba($gray, 0.3);
      }
    }
  }
}

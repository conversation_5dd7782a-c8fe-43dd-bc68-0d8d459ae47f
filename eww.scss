// Variables
$bg: #2a2a2a;
$fg: #f8f8f2;
$accent: #bd93f9;
$gray: #44475a;
$light-gray: #6272a4;
$red: #ff5555;

// Estilos generales
* {
  all: unset;
  font-family: 'Fira Code', 'Font Awesome 6 Free';
  font-size: 12px;
}

// Contenedor principal del calendario
.calendar-container {
  background-color: $bg;
  border-radius: 12px;
  padding: 12px;
  color: $fg;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.5);
}

// Encabezado del calendario
.calendar-header {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// Botón de cierre
.close-button {
  color: $light-gray;
  font-size: 18px;
  margin-right: 8px;
  margin-left: auto;
  cursor: pointer;

  &:hover {
    color: $red;
  }
}

// Navegación de mes
.month-navigation {
  margin: 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .month-year {
    font-size: 14px;
    font-weight: bold;
    margin: 0 12px;
  }

  .nav-button {
    background-color: $gray;
    color: $fg;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;

    &:hover {
      background-color: $light-gray;
    }
  }
}

// Días de la semana
.weekdays {
  margin: 8px 0;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;

  .weekday {
    font-weight: bold;
    color: $accent;
    min-width: 30px;
  }
}

// Cuadrícula del calendario
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;

  .day {
    min-width: 30px;
    min-height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &.current-month {
      color: $fg;

      &.today {
        background-color: $accent;
        color: $bg;
        font-weight: bold;
      }

      &:hover {
        background-color: $gray;
      }
    }

    &.other-month {
      color: rgba($fg, 0.5);
    }
  }
}
}

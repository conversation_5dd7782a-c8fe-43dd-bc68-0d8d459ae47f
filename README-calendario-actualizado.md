# Widget de Calendario para EWW

Este es un widget de calendario mensual para EWW (ElKowar's Wacky Widgets) que muestra un calendario completo con navegación entre meses.

## ✨ Características

- **Calendario mensual completo**: Muestra todos los días del mes en una cuadrícula de 7x6
- **Navegación entre meses**: Botones para ir al mes anterior y siguiente
- **Día actual resaltado**: El día de hoy se muestra con un color diferente
- **Días de otros meses**: Se muestran los días del mes anterior y siguiente para completar la cuadrícula
- **Diseño responsivo**: Se adapta bien a diferentes tamaños de pantalla
- **Estilo moderno**: Colores y efectos hover atractivos
- **Sin dependencias externas**: No requiere `rofi` o `dzen`

## 📁 Estructura de archivos

```
~/.config/eww/
├── eww.yuck                    # Configuración principal de EWW
├── eww.scss                    # Estilos CSS del calendario
├── widgets/
│   └── calendar/
│       ├── calendar.yuck       # Widget del calendario
│       └── calendar_utils.yuck # Utilidades y funciones
└── scripts/
    ├── calendar.sh             # Script principal del calendario
    ├── get-calendar-days.sh    # Genera los días del mes
    └── test-calendar.sh        # Script de prueba
```

## 🚀 Instalación

1. **Dependencias necesarias**:
   ```bash
   sudo apt install jq  # Para procesamiento JSON
   ```

2. **Los archivos ya están configurados** en `~/.config/eww/`

3. **Hacer scripts ejecutables**:
   ```bash
   chmod +x ~/.config/eww/scripts/*.sh
   ```

## 📖 Uso

### Comandos básicos
```bash
# Abrir el calendario
eww open calendar

# Cerrar el calendario
eww close calendar

# Recargar la configuración
eww reload

# Probar el calendario
bash ~/.config/eww/scripts/test-calendar.sh
```

### Navegación
- **Flecha izquierda (‹)**: Ir al mes anterior
- **Flecha derecha (›)**: Ir al mes siguiente
- **Botón X**: Cerrar el calendario
- **Click en cualquier día**: Volver al mes actual

## 🎨 Personalización

### Colores
Modifica las variables en `eww.scss`:
```scss
$bg: #2a2a2a;        // Color de fondo
$fg: #f8f8f2;        // Color del texto
$accent: #bd93f9;    // Color de acento (día actual)
$gray: #44475a;      // Color gris
$light-gray: #6272a4; // Color gris claro
$red: #ff5555;       // Color rojo (botón cerrar)
```

### Posición de la ventana
Modifica la geometría en `eww.yuck`:
```yuck
:geometry (geometry :x "90%"      ; Posición horizontal
                   :y "5%"        ; Posición vertical
                   :width "300px" ; Ancho
                   :height "400px"; Alto
                   :anchor "top right") ; Anclaje
```

### Intervalo de actualización
Cambia el intervalo en `calendar_utils.yuck`:
```yuck
(defpoll get-calendar :interval "5s"  ; Cada 5 segundos
  "bash ~/.config/eww/scripts/calendar.sh")
```

## 🔧 Solución de problemas

### El calendario no se abre
1. Verifica que EWW esté ejecutándose:
   ```bash
   eww daemon
   ```
2. Ejecuta el script de prueba:
   ```bash
   bash ~/.config/eww/scripts/test-calendar.sh
   ```

### Los días no se muestran correctamente
1. Verifica que `jq` esté instalado
2. Comprueba que los scripts tengan permisos de ejecución:
   ```bash
   chmod +x ~/.config/eww/scripts/*.sh
   ```

### Errores de CSS
- EWW tiene limitaciones en las propiedades CSS soportadas
- Evita usar propiedades como `display: flex`, `grid`, `transform`, etc.
- Usa solo propiedades básicas como `color`, `background-color`, `padding`, `margin`

## 📋 Archivos de configuración

| Archivo | Descripción |
|---------|-------------|
| `eww.yuck` | Definición de la ventana del calendario |
| `calendar.yuck` | Widget del calendario con estructura |
| `calendar_utils.yuck` | Funciones de polling para datos |
| `calendar.sh` | Lógica del calendario y navegación |
| `get-calendar-days.sh` | Genera JSON con días (42 días total) |
| `eww.scss` | Estilos CSS con variables de color |

## 🎯 Características técnicas

- **Cuadrícula de 7x6**: Siempre muestra 42 días (6 semanas completas)
- **Navegación persistente**: Mantiene el estado del mes seleccionado
- **Actualización automática**: Se actualiza cada 5 segundos
- **Compatibilidad**: Funciona con EWW sin dependencias externas
- **Responsive**: Se adapta a diferentes resoluciones

## 🤝 Contribuir

Si encuentras bugs o quieres mejorar el calendario:
1. Modifica los archivos necesarios
2. Prueba los cambios con `bash scripts/test-calendar.sh`
3. Recarga EWW con `eww reload`
4. Abre el calendario con `eww open calendar`

---

**¡Disfruta de tu nuevo widget de calendario para EWW!** 📅

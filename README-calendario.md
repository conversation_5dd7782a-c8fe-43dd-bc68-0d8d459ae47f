# Widget de Calendario para EWW

Este es un widget de calendario mensual para [EWW (ElKowar's Wacky Widgets)](https://github.com/elkowar/eww) que muestra un calendario interactivo con navegación entre meses y resaltado del día actual.

## Características

- Muestra el mes y año actual
- Navegación entre meses con botones
- Días de la semana claramente etiquetados
- Días del mes organizados en una cuadrícula
- Día actual resaltado
- Diseño responsivo y personalizable

## Requisitos

- [EWW](https://github.com/elkowar/eww) instalado y configurado
- `jq` para el procesamiento de JSON
- Fuente 'Fira Code' o cualquier otra fuente monoespaciada

## Instalación

1. Copia los archivos del widget a tu directorio de configuración de EWW:

```bash
# Crear directorios necesarios
mkdir -p ~/.config/eww/widgets/calendar
mkdir -p ~/.config/eww/scripts

# Copiar archivos del widget
cp -r widgets/calendar/* ~/.config/eww/widgets/calendar/
cp scripts/calendar.sh ~/.config/eww/scripts/
cp scripts/get-calendar-days.sh ~/.config/eww/scripts/
cp scripts/get-day-class.sh ~/.config/eww/scripts/

# Hacer ejecutables los scripts
chmod +x ~/.config/eww/scripts/*.sh
```

2. Asegúrate de que tu archivo `~/.config/eww/eww.yuck` incluya el widget:

```yuck
; En tu archivo eww.yuck
(include "./widgets/calendar/calendar_utils.yuck")
(include "./widgets/calendar/calendar.yuck")

; Definir la ventana del calendario
(defwindow calendar
  :monitor 0
  :geometry (geometry :x "90%"
                     :y "5%"
                     :width "300px"
                     :height "auto"
                     :anchor "top right")
  :stacking "fg"
  :focusable true
  :screen -1
  :visible false
  
  (calendar-widget))
```

3. Asegúrate de que tu archivo `~/.config/eww/eww.scss` incluya los estilos del calendario o copia los estilos proporcionados.

## Uso

Para abrir el calendario, ejecuta:

```bash
eww open calendar
```

Para cerrar el calendario, haz clic en el botón de cierre (×) en la esquina superior derecha.

## Personalización

### Cambiar colores
Puedes personalizar los colores editando las variables en `eww.scss`:

```scss
// Variables de colores
$bg: #2a2a2a;           // Fondo del calendario
$fg: #f8f8f2;           // Color de texto principal
$accent: #bd93f9;       // Color de acento (día actual, encabezados)
$gray: #44475a;         // Color de fondo de botones
$light-gray: #6272a4;   // Color de fondo al pasar el ratón
$red: #ff5555;          // Color del botón de cierre al pasar el ratón
```

### Cambiar tamaño de fuente
Para cambiar el tamaño de la fuente, modifica la propiedad `font-size` en la sección de estilos generales:

```scss
* {
  all: unset;
  font-family: 'Fira Code', 'Font Awesome 6 Free';
  font-size: 14px;  // Ajusta este valor según prefieras
}
```

## Solución de problemas

### El calendario no se muestra
- Asegúrate de que el demonio de EWW esté en ejecución: `eww daemon`
- Verifica que todos los scripts tengan permisos de ejecución: `chmod +x ~/.config/eww/scripts/*.sh`
- Revisa los registros de EWW para ver errores: `eww logs`

### Los botones de navegación no funcionan
- Asegúrate de que `jq` esté instalado: `sudo apt install jq`
- Verifica que las rutas a los scripts en `calendar.yuck` sean correctas

## Licencia

Este proyecto está bajo la Licencia MIT. Siéntete libre de usarlo y modificarlo según tus necesidades.

## Créditos

Desarrollado como parte de un proyecto de widget para EWW.

; EWW Configuration
; Archivo principal de configuración de EWW

; Incluir utilidades del calendario
(include "./widgets/calendar/calendar_utils.yuck")

; Incluir widgets
(include "./widgets/calendar/calendar.yuck")

; Definir la ventana del calendario
(defwindow calendar
  :monitor 0
  :geometry (geometry :x "90%"
                     :y "5%"
                     :width "300px"
                     :height "400px"
                     :anchor "top right")
  :stacking "fg"
  :focusable true
  :visible false

  ; Contenido del calendario
  (calendar-widget))

(defwindow control_center
  :geometry (geometry :x "1720px" :y "40px" :width "300px")
  :stacking "fg"
  :focusable false
  :visible false
  (box :class "control-center"
    (box :class "volume"
      (button :onclick "eww update volume=$(~/.config/eww/scripts/ControlCenter/volume.sh)" :class "volume-button" "🔊")
      (label :class "volume-label" :text (get (get-volume) "volume")))))

; Polls para el calendario ya están definidos en calendar_utils.yuck
(defpoll volume :interval "2s" "bash ~/.config/eww/scripts/ControlCenter/volume.sh")
(defpoll brightness :interval "5s" "bash ~/.config/eww/scripts/ControlCenter/brightness.sh")
(defpoll battery :interval "10s" "bash ~/.config/eww/scripts/ControlCenter/battery.sh")
(defpoll network :interval "10s" "bash ~/.config/eww/scripts/ControlCenter/network.sh")
(defpoll media :interval "10s" "bash ~/.config/eww/scripts/ControlCenter/media.sh")

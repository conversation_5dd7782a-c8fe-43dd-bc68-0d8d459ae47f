(defwidget control-center []
  (box :class "control-center" :orientation "vertical"
    (label :text "🛠 Centro de Control" :class "title")

    ;; Volumen
    (box :class "item"
      (label :text "🔊 Volumen")
      (slider :value volume :onchange "pamixer --set-volume {0}" :min 0 :max 100)
    )

    ;; Brillo
    (box :class "item"
      (label :text "💡 Brillo")
      (slider :value brightness :onchange "brightnessctl set {0}%" :min 1 :max 100)
    )

    ;; Red
    (box :class "item"
      (label :text "🌐 Red")
      (label :text network)
    )

    ;; Batería
    (box :class "item"
      (label :text "🔋 Batería")
      (label :text battery)
    )

    ;; Controles multimedia
    (box :class "item controls"
      (button :onclick "playerctl previous" "⏮")
      (button :onclick "playerctl play-pause" "⏯")
      (button :onclick "playerctl next" "⏭")
    )

    ;; Botones de sesión
    (box :class "item session"
      (button :onclick "loginctl lock-session" "🔒 Bloquear")
      (button :onclick "systemctl suspend" "💤 Suspender")
      (button :onclick "systemctl reboot" "🔄 Reiniciar")
      (button :onclick "systemctl poweroff" "⏻ Apagar")
    )
  )
)

; Widget de Calendario para EWW
; Ubicación: ~/.config/eww/widgets/calendar/

(defwidget calendar-widget []
  (box :class "calendar-container"
    (box :class "calendar-header"
      (button :onclick "eww close calendar"
        :class "close-button"
        "×")
      (box :class "month-navigation"
        (button :onclick "~/.config/eww/scripts/calendar.sh prev && eww update calendar-days=$(~/.config/eww/scripts/get-calendar-days.sh)" 
                :class "nav-button" "‹")
        (label :class "month-year" :text (get (get-calendar) "month"))
        (button :onclick "~/.config/eww/scripts/calendar.sh next && eww update calendar-days=$(~/.config/eww/scripts/get-calendar-days.sh)" 
                :class "nav-button" "›")))
    
    (box :class "weekdays"
      (label :class "weekday" "Lu")
      (label :class "weekday" "Ma")
      (label :class "weekday" "Mi")
      (label :class "weekday" "Ju")
      (label :class "weekday" "Vi")
      (label :class "weekday" "Sá")
      (label :class "weekday" "Do"))
    
    (box :class "calendar-grid"
      (for day in (get-calendar-days)
        (box :class (get-day-class day)
          (label :text (get day "day") 
                 :class (get-day-label-class day)))))))

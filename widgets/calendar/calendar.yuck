; Widget de Calendario para EWW
; Ubicación: ~/.config/eww/widgets/calendar/

(defwidget calendar-widget []
  (box :class "calendar-container"
       :orientation "v"
       :space-evenly false

    ; Encabezado con navegación
    (box :class "calendar-header"
         :orientation "h"
         :space-evenly false
      (box :class "month-navigation"
           :orientation "h"
           :space-evenly false
           :halign "center"
        (button :onclick "bash ~/.config/eww/scripts/calendar.sh prev"
                :class "nav-button"
                "‹")
        (label :class "month-year"
               :text {get-calendar.month}
               :halign "center")
        (button :onclick "bash ~/.config/eww/scripts/calendar.sh next"
                :class "nav-button"
                "›"))
      (button :onclick "eww close calendar"
              :class "close-button"
              :halign "end"
              "×"))

    ; Días de la semana
    (box :class "weekdays"
         :orientation "h"
         :space-evenly true
      (label :class "weekday" "Lun")
      (label :class "weekday" "Mar")
      (label :class "weekday" "Mié")
      (label :class "weekday" "Jue")
      (label :class "weekday" "Vie")
      (label :class "weekday" "Sáb")
      (label :class "weekday" "Dom"))

    ; Cuadr<PERSON><PERSON> de días (usando CSS Grid)
    (box :class "calendar-grid"
      (for day in get-calendar-days
        (button :class "day ${day.is_current_month == 'true' ? 'current-month' : 'other-month'} ${day.is_today == 'true' ? 'today' : ''}"
                :onclick "bash ~/.config/eww/scripts/calendar.sh reset"
          (label :text {day.day}
                 :class "day-label"))))))

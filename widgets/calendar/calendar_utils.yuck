; Utilidades para el widget de calendario

; Función para obtener los datos del calendario actual
(defpoll get-calendar :interval "1h"
  "~/.config/eww/scripts/calendar.sh")

; Función para obtener los días del mes actual
(defpoll get-calendar-days :interval "1h"
  "~/.config/eww/scripts/get-calendar-days.sh")

; Función para obtener la clase CSS de un día
(defn get-day-class [day]
  (let ((is-current-month (get day "is_current_month"))
        (is-today (get day "is_today")))
    (str "day " (if (= is-current-month "true") 
                   (str "current-month" (if (= is-today "true") " today" ""))
                   "other-month"))))

; Función para obtener la clase de la etiqueta del día
(defn get-day-label-class [day]
  (if (= (get day "is_today") "true") 
    "day-label today" 
    "day-label"))
